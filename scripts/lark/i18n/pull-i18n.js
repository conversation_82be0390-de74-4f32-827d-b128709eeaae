const glob = require('glob')
const util = require('util')
const path = require('path')
const fs = require('fs')
const _ = require('lodash')
const mkdirp = require('mkdirp')
const exec = util.promisify(require('child_process').exec)

const feishu = require('./lark-docs-util.js')

const { execQueue } = require('./queue')
const { isArray, isString } = require('lodash')
const cwd = process.cwd()

let translationsPath

// const domains = i18nNamespace;

async function writeCSVData(remoteRows, locales, domain) {
  await execQueue(
    locales.map((locale, localeIndex) => async () => {
      if (locale === 'jp') {
        locale = 'ja'
      }
      const filepath = path.join(
        translationsPath,
        locale || '',
        `${domain}.json`
      )
      if (!locale) return
      let data = {}
      if (!fs.existsSync(filepath)) {
        mkdirp.sync(path.parse(filepath).dir)
      }
      // const
      let stringsCount = 0
      remoteRows.slice(1).forEach((row) => {
        const k = row[0]
        let v = row[localeIndex + 1]
        if (isArray(v)) {
          v = v.map((_i) => _i.text).join('')
        }
        if (v && k) {
          const conflictValue = _.get(data, k)
          if (conflictValue && !_.isString(conflictValue)) {
            throw new Error(
              `I18N key conflict: [key:${k}];[value:${v}];[conflictValue:${JSON.stringify(
                conflictValue
              )}]}`
            )
          }
          _.set(data, k, v)
          stringsCount++
        }
      })
      fs.writeFileSync(
        filepath,
        JSON.stringify(data, null, 2)
      )

      console.log(
        `Write [${locale}]: ${stringsCount} strings`
      )
    })
  )
}

async function main(app) {
  const appPath = path.join(cwd, 'apps', app)
  translationsPath = path.join(appPath, 'public', 'locales')

  await feishu.getToken()

  const sheetKey = 'LWRKsETd8hIp3ut6CVxj9ZGepSe'
  const { sheets } = await feishu.getSpreadsheets(sheetKey)
  const list = sheets
  await execQueue(
    list.map((sheet, index) => async () => {
      const remoteRows = await feishu.readSheetRows(
        sheet.sheetId,
        sheet.rowCount,
        sheet.columnCount,
        sheetKey
      )
      const locales = remoteRows[0].slice(1)

      await writeCSVData(remoteRows, locales, sheet.title)
      console.log(
        `[Pull] ${appPath}/public/locales/[lng]/${
          sheet.title
        }.json updated rows:${remoteRows.length}. (${
          index + 1
        }/${list.length})`
      )
    })
  )
}

module.exports = main
main('next-app')
